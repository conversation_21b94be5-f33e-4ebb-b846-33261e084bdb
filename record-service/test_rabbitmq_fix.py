#!/usr/bin/env python3
"""
Test script to verify the RabbitMQ publishing fix.
This test verifies that the should_publish_folder flag works correctly.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch, AsyncMock

# Mock environment variables before importing main
os.environ.update({
    'GCS_BUCKET_NAME': 'test-bucket',
    'RABBITMQ_HOST': 'localhost',
    'RABBITMQ_PORT': '5672',
    'RABBITMQ_USER': 'test',
    'RABBITMQ_PASSWORD': 'test',
    'RABBITMQ_QUEUE_NAME': 'test-queue',
    'RABBITMQ_EXCHANGE_NAME': 'test-exchange',
    'RABBITMQ_EXCHANGE_TYPE': 'direct',
    'RABBITMQ_PUBLISH_QUEUE_NAME': 'test-publish-queue',
    'LIVEKIT_SERVER_URL': 'ws://localhost:7880',
    'LIVEKIT_API_KEY': 'test-key',
    'LIVEKIT_API_SECRET': 'test-secret'
})

# Mock the Google Cloud Storage and pika imports
with patch('google.cloud.storage.Client'), \
     patch('pika.PlainCredentials'), \
     patch('pika.BlockingConnection'), \
     patch('pika.ConnectionParameters'):

    from main import ApplicationState, app_state, handle_stop_recording, publish_message_to_queue

class TestRabbitMQFix(unittest.TestCase):

    def setUp(self):
        """Reset app_state before each test"""
        app_state.tasks = {}
        app_state.background_tasks = set()
        app_state.is_recording = False
        app_state.egress_info = {}
        app_state.should_publish_folder = False

    @patch('main.channel')
    def test_publish_message_to_queue(self, mock_channel):
        """Test that publish_message_to_queue works correctly"""
        test_message = "test-folder-path"

        # Call the function
        publish_message_to_queue(test_message)

        # Verify the channel.basic_publish was called with correct parameters
        mock_channel.basic_publish.assert_called_once()
        call_args = mock_channel.basic_publish.call_args

        # Check the arguments
        self.assertEqual(call_args[1]['routing_key'], 'test-publish-queue')
        self.assertEqual(call_args[1]['body'], test_message.encode('utf-8'))

    @patch('main.api.LiveKitAPI')
    @patch('main.publish_message_to_queue')
    async def test_handle_stop_recording_publishes_immediately(self, mock_publish, mock_livekit_api):
        """Test that handle_stop_recording publishes message immediately"""
        # Setup
        mock_room = MagicMock()
        mock_room.local_participant.publish_data = AsyncMock()

        # Mock the LiveKit API
        mock_api_instance = MagicMock()
        mock_api_instance.egress.stop_egress = AsyncMock()
        mock_livekit_api.return_value = mock_api_instance

        # Add some test data
        app_state.tasks = {'track_1': True}
        app_state.egress_info = {'track_1': 'egress_id_1'}

        # Call the function
        result = await handle_stop_recording(mock_room)

        # Verify the message was published immediately
        mock_publish.assert_called_once()
        self.assertEqual(result, {"status": "Recording stopped"})

        # Verify the egress_info was cleared
        self.assertEqual(len(app_state.egress_info), 0)

        # Verify publish_data was called
        mock_room.local_participant.publish_data.assert_called_once()

if __name__ == '__main__':
    import asyncio

    # Run async tests
    async def run_async_tests():
        test = TestRabbitMQFix()
        test.setUp()
        await test.test_handle_stop_recording_publishes_immediately()
        print("✓ Async test passed: handle_stop_recording publishes immediately")

    # Run sync tests
    unittest.main(argv=[''], exit=False, verbosity=2)

    # Run async test
    print("\nRunning async tests...")
    asyncio.run(run_async_tests())
    print("✓ All tests passed!")
