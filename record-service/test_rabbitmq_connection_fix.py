#!/usr/bin/env python3
"""
Test script to verify the RabbitMQ connection thread safety fix.
This test verifies that separate connections are used for publishing.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch, call
import threading
import time

# Mock environment variables before importing main
os.environ.update({
    'GCS_BUCKET_NAME': 'test-bucket',
    'RABBITMQ_HOST': 'localhost',
    'RABBITMQ_PORT': '5672',
    'RABBITMQ_USER': 'test',
    'RABBITMQ_PASSWORD': 'test',
    'RABBITMQ_QUEUE_NAME': 'test-queue',
    'RABBITMQ_EXCHANGE_NAME': 'test-exchange',
    'RABBITMQ_EXCHANGE_TYPE': 'direct',
    'RABBITMQ_PUBLISH_QUEUE_NAME': 'test-publish-queue',
    'LIVEKIT_SERVER_URL': 'ws://localhost:7880',
    'LIVEKIT_API_KEY': 'test-key',
    'LIVEKIT_API_SECRET': 'test-secret'
})

class TestRabbitMQConnectionFix(unittest.TestCase):

    @patch('pika.BlockingConnection')
    @patch('pika.PlainCredentials')
    def test_publish_creates_separate_connection(self, mock_credentials, mock_connection):
        """Test that publish_message_to_queue creates a separate connection"""

        # Mock the connection and channel
        mock_conn_instance = MagicMock()
        mock_channel_instance = MagicMock()
        mock_connection.return_value = mock_conn_instance
        mock_conn_instance.channel.return_value = mock_channel_instance
        mock_channel_instance.is_closed = False
        mock_conn_instance.is_closed = False

        # Import after mocking
        from main import publish_message_to_queue

        # Call the function
        test_message = "test_folder_path"
        publish_message_to_queue(test_message)

        # Verify a new connection was created
        mock_connection.assert_called()

        # Verify channel operations
        mock_channel_instance.exchange_declare.assert_called_once()
        mock_channel_instance.queue_declare.assert_called_once()
        mock_channel_instance.basic_publish.assert_called_once()

        # Verify connection cleanup
        mock_channel_instance.close.assert_called_once()
        mock_conn_instance.close.assert_called_once()

    @patch('pika.BlockingConnection')
    @patch('pika.PlainCredentials')
    def test_multiple_concurrent_publishes(self, mock_credentials, mock_connection):
        """Test that multiple concurrent publishes work without interference"""

        # Mock the connection and channel
        mock_conn_instance = MagicMock()
        mock_channel_instance = MagicMock()
        mock_connection.return_value = mock_conn_instance
        mock_conn_instance.channel.return_value = mock_channel_instance
        mock_channel_instance.is_closed = False
        mock_conn_instance.is_closed = False

        # Import after mocking
        from main import publish_message_to_queue

        # Function to run in threads
        def publish_in_thread(message):
            try:
                publish_message_to_queue(f"message_{message}")
                return True
            except Exception as e:
                print(f"Error in thread {message}: {e}")
                return False

        # Create multiple threads
        threads = []
        results = []

        for i in range(5):
            thread = threading.Thread(target=lambda i=i: results.append(publish_in_thread(i)))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify all publishes succeeded
        self.assertEqual(len(results), 5)

        # Verify multiple connections were created (one per publish)
        self.assertEqual(mock_connection.call_count, 5)

    @patch('pika.BlockingConnection')
    @patch('pika.PlainCredentials')
    def test_connection_cleanup_on_error(self, mock_credentials, mock_connection):
        """Test that connections are properly cleaned up even when errors occur"""

        # Mock the connection and channel
        mock_conn_instance = MagicMock()
        mock_channel_instance = MagicMock()
        mock_connection.return_value = mock_conn_instance
        mock_conn_instance.channel.return_value = mock_channel_instance
        mock_channel_instance.is_closed = False
        mock_conn_instance.is_closed = False

        # Make basic_publish raise an exception
        mock_channel_instance.basic_publish.side_effect = Exception("Publish failed")

        # Import after mocking
        from main import publish_message_to_queue

        # Call the function and expect it to raise
        with self.assertRaises(Exception):
            publish_message_to_queue("test_message")

        # Verify connection cleanup still happened
        mock_channel_instance.close.assert_called_once()
        mock_conn_instance.close.assert_called_once()

    @patch('pika.ConnectionParameters')
    @patch('pika.BlockingConnection')
    @patch('pika.PlainCredentials')
    def test_create_rabbitmq_connection_function(self, mock_credentials, mock_connection, mock_params):
        """Test the create_rabbitmq_connection helper function"""

        # Mock the connection and parameters
        mock_conn_instance = MagicMock()
        mock_params_instance = MagicMock()
        mock_connection.return_value = mock_conn_instance
        mock_params.return_value = mock_params_instance

        # Import after mocking
        from main import create_rabbitmq_connection

        # Call the function
        connection = create_rabbitmq_connection()

        # Verify connection was created
        mock_connection.assert_called_once_with(mock_params_instance)

        # Verify ConnectionParameters was called with proper parameters
        mock_params.assert_called_once()
        call_kwargs = mock_params.call_args[1]  # Get the keyword arguments

        # Verify the connection parameters include heartbeat and timeout
        self.assertEqual(call_kwargs['heartbeat'], 600)
        self.assertEqual(call_kwargs['blocked_connection_timeout'], 300)

        # Verify the returned connection is the mocked instance
        self.assertEqual(connection, mock_conn_instance)

if __name__ == '__main__':
    # Mock the Google Cloud Storage and initial pika imports
    with patch('google.cloud.storage.Client'), \
         patch('pika.PlainCredentials'), \
         patch('pika.BlockingConnection'), \
         patch('pika.ConnectionParameters'):

        unittest.main(verbosity=2)
