import logging
import logging.config
import os
import threading
import pika
from google.cloud import storage
import mediapipe as mp
from mediapipe.tasks import python
from mediapipe.tasks.python import vision
from mediapipe.framework.formats import landmark_pb2
from mediapipe import solutions
import cv2

logging.config.fileConfig("log_conf.ini")
LOGGER = logging.getLogger(__name__)

# Initialize Google Cloud Storage client
storage_client = storage.Client()
bucket_name = os.getenv('GCS_BUCKET_NAME')


queue_host = os.getenv('RABBITMQ_HOST')
queue_port = os.getenv('RABBITMQ_PORT')
queue_user = os.getenv('RABBITMQ_USER')
queue_password = os.getenv('RABBITMQ_PASSWORD')
queue_name = os.getenv('RABBITMQ_QUEUE_NAME')
exchange_name = os.getenv('RABBITMQ_EXCHANGE_NAME')
exchange_type = os.getenv('RABBITMQ_EXCHANGE_TYPE')

credentials = pika.PlainCredentials(queue_user, queue_password)
connection = pika.BlockingConnection(pika.ConnectionParameters(host=queue_host,port=queue_port,credentials=credentials))
channel = connection.channel()
channel.basic_qos(prefetch_count=1)
channel.exchange_declare(exchange=exchange_name, exchange_type=exchange_type, passive=True)
queue = channel.queue_declare(queue=queue_name, passive=True)

# Get the message count
message_count = queue.method.message_count

# Check if the queue is empty
if message_count == 0:
    LOGGER.info("Don't have any message in queue. Stop the server.")
    os._exit(0)

base_options = python.BaseOptions(model_asset_path='./pose_landmarker_heavy.task')
options = vision.PoseLandmarkerOptions(
    base_options=base_options,
    output_segmentation_masks=False,
    min_pose_presence_confidence = float(os.getenv("MP_MIN_POSE_PRESENCE_CONFIDENCE")),
    min_tracking_confidence = float(os.getenv("MP_MIN_TRACKING_CONFIDENCE")),
    min_pose_detection_confidence = float(os.getenv("MP_MIN_POSE_DETECTION_CONFIDENCE")),
    )
detector = vision.PoseLandmarker.create_from_options(options)
fakeImage = cv2.imread("./fake_image.png")

def stop_server(queue_channel, queue_method):
    LOGGER.info("Stop streaming EXIT server")
    try:
        # Delete received message from queue
        queue_channel.basic_ack(delivery_tag=queue_method.delivery_tag)
        os._exit(0)
    except Exception as e:
        LOGGER.error(f"Error in stop_server: {str(e)}", exc_info=True)
        os._exit(1)
    finally:
        LOGGER.info("Stop heart_beat_thread due to metadata")

def process_frame_with_pose(frame_rgb):
    try:
        # Convert numpy array to MediaPipe Image
        mp_image = mp.Image(image_format=mp.ImageFormat.SRGB, data=frame_rgb)

        # Process the frame
        results = detector.detect(mp_image)
        pose_landmarks_list = results.pose_landmarks
        if pose_landmarks_list is None or len(pose_landmarks_list) == 0:
            return None

        # Draw pose landmarks
        for idx in range(len(pose_landmarks_list)):
            pose_landmarks = pose_landmarks_list[idx]
            # Get ear positions (x,y) from landmarks
            left_ear_x = int(pose_landmarks[7].x * frame_rgb.shape[1])
            left_ear_y = int(pose_landmarks[7].y * frame_rgb.shape[0])
            right_ear_x = int(pose_landmarks[8].x * frame_rgb.shape[1])
            right_ear_y = int(pose_landmarks[8].y * frame_rgb.shape[0])

            # Calculate face region with padding
            face_top = max(0, min(left_ear_y, right_ear_y) - 50)
            face_bottom = min(frame_rgb.shape[0], max(left_ear_y, right_ear_y) + 50)
            face_left = max(0, min(left_ear_x, right_ear_x) - 50)
            face_right = min(frame_rgb.shape[1], max(left_ear_x, right_ear_x) + 50)

            # Get the face region directly (no caching for dancing scenarios)
            face_region = frame_rgb[face_top:face_bottom, face_left:face_right]

            # Only replace if we have a valid region
            if face_region.size > 0:
                # Resize fake image to match face region
                replacing_face = cv2.resize(fakeImage, (face_region.shape[1], face_region.shape[0]))
                # Replace the face region
                frame_rgb[face_top:face_bottom, face_left:face_right] = replacing_face

            # Draw the pose landmarks.
            pose_landmarks_proto = landmark_pb2.NormalizedLandmarkList()
            pose_landmarks_proto.landmark.extend([
            landmark_pb2.NormalizedLandmark(x=landmark.x, y=landmark.y, z=landmark.z) for landmark in pose_landmarks
            ])
            solutions.drawing_utils.draw_landmarks(
            frame_rgb,
            pose_landmarks_proto,
            solutions.pose.POSE_CONNECTIONS,
            solutions.drawing_styles.get_default_pose_landmarks_style())

        return frame_rgb
    except Exception as e:
        LOGGER.error(f"Error in process_frame_with_pose: {str(e)}", exc_info=True)
        return None

def callback(ch, method, properties, body):
    LOGGER.info(f" [x] Received {body}")

    # Check if the message came from the expected queue by checking the routing key
    # In RabbitMQ, when using direct exchange, the routing key should match the queue name
    if hasattr(method, 'routing_key') and method.routing_key != queue_name:
        LOGGER.info(f"Ignoring message from queue '{method.routing_key}', expected '{queue_name}'")
        # Acknowledge the message to remove it from the queue but don't process it
        # ch.basic_ack(delivery_tag=method.delivery_tag)
        return

    thread = threading.Thread(target=start_process, args=(ch, body.decode('utf-8'), method))
    thread.start()

def start_process(queue_channel, message, queue_method):
    try:
        folder_path = message.strip()
        bucket = storage_client.bucket(bucket_name)
        blobs = bucket.list_blobs(prefix=f"{folder_path}/origin")

        file_paths = []
        for blob in blobs:
            if blob.name.endswith('.webm'):  # Exclude "folders"
                file_paths.append(blob.name)
        LOGGER.info(f"List of files to process: {file_paths}")
        for file_path in file_paths:
            try:
                file_name = file_path.split('/')[-1].replace('.webm', '.mp4')
                # Check if file exists in {folder_path}/origin in bucket
                anyonymized_blob_path = f"{folder_path}/anonymized/{file_name}"
                if bucket.blob(anyonymized_blob_path).exists():
                    LOGGER.warning(f"File {anyonymized_blob_path} already exist in bucket. Skipping.")
                    continue
                LOGGER.info(f"Processing video {file_name}")
                blob = bucket.blob(file_path)
                video_data = blob.download_as_bytes()
                video_capture = cv2.VideoCapture()
                video_capture.open(cv2.samples.findFileOrKeep(video_data))
                video_writers = cv2.VideoWriter(
                    file_name,
                    cv2.VideoWriter_fourcc(*'MP4V'),
                    30.0,
                    (video_capture.get(3), video_capture.get(4)),
                    isColor=True
                )
                frame_count = 0
                while True:
                    ret, frame = video_capture.read()
                    if not ret:
                        break
                    frame_count += 1
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    processed_frame = process_frame_with_pose(frame_rgb)
                    if processed_frame is not None:
                        video_writers.write(processed_frame)

                video_writers.release()
                blob = bucket.blob(f"{folder_path}/anonymized/{file_name}")
                blob.upload_from_filename(file_name, timeout=86400)
                os.remove(file_name)
                LOGGER.info(f"DONE video {file_name}")
            except Exception as e:
                LOGGER.error(f"Error processing video {file_path}: {str(e)}", exc_info=True)
    except Exception as e:
        LOGGER.error(f"Error in start_process: {str(e)}", exc_info=True)
        os._exit(1)
    finally:
        # Ensure we acknowledge the message even if there's an error
        try:
            queue_channel.basic_ack(delivery_tag=queue_method.delivery_tag)
        except Exception as e:
            LOGGER.error(f"Error acknowledging message: {str(e)}", exc_info=True)

channel.basic_consume(queue=queue_name, on_message_callback=callback, auto_ack=False)
LOGGER.info(' [*] Waiting for messages. To exit press CTRL+C')
channel.start_consuming()